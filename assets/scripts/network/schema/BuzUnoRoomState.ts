/**
 * UNO 游戏房间状态定义
 * 转换自 Colyseus Schema 为标准 TypeScript 类型
 */

export class UserState {
  //当前用户状态
  state: number;
  //仅在state为[WATCHING]才有意义
  //观察哪个用户
  watchUid?: string;

  //准备状态
  static readonly READY = 0;
  //没有准备
  static readonly NOT_READY = 1;
  //正在游戏
  static readonly PLAYING = 2;
  //观察模式
  static readonly WATCHING = 3; // 修正：应该是3，不是2（与PLAYING重复了）

  constructor() {
    this.state = UserState.NOT_READY;
  }
}

export class Player {
  name: string;
  uid: string;
  avatar: string;
  stateWrapper: UserState;

  constructor(name: string, uid: string, avatar: string, state: UserState) {
    this.name = name;
    this.uid = uid;
    this.avatar = avatar;
    this.stateWrapper = state;
  }
}

export class UnoRoomState {
  //当前所有用户
  userList: Player[] = [];
  //当前哪个用户出牌 记录用户的 UID
  currentTurn?: string;

  constructor() {
    this.userList = [];
  }
}