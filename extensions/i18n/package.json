{"package_version": 2, "version": "1.0.1", "name": "i18n", "description": "i18n:i18n.description", "main": "./dist/main.js", "devDependencies": {"@types/node": "^16.9.1", "@types/vue": "^2.0.0", "typescript": "^4.3.4"}, "panels": {"default": {"title": "本地化控制面板", "main": "./dist/panels/default.js"}}, "author": "VisualSJ", "editor": ">=3.1.0", "scripts": {"build": "tsc -b", "watch": "tsc -w"}, "contributions": {"messages": {"open-default-panel": {"methods": ["openDefaultPanel"]}}, "menu": [{"path": "i18n:menu.extension", "label": "I18n Settings", "message": "open-default-panel"}], "scene": {"script": "./dist/scene.js"}, "asset-db": {"mount": {"path": "./assets"}}, "profile": {"project": {"first": {"type": "boolean", "default": false}}}}, "dependencies": {"vue": "^2.6.14"}, "_storeId": "d66f5e0254969575db69b997744fbf24"}