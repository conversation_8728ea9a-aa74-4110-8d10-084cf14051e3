import { assetManager, ImageAsset, Node, Sprite, SpriteFrame, Texture2D } from 'cc';

/**
 * 头像加载工具类
 * 用于加载网络头像并设置到指定的节点上
 */
export class AvatarLoader {
    
    /**
     * 加载网络头像并设置到指定节点
     * @param avatarUrl 头像URL
     * @param targetNode 目标节点（应该包含Sprite组件）
     * @param onSuccess 成功回调
     * @param onError 失败回调
     */
    static loadAvatar(
        avatarUrl: string, 
        targetNode: Node, 
        onSuccess?: () => void, 
        onError?: (error: any) => void
    ): void {
        if (!avatarUrl || !targetNode) {
            console.error("AvatarLoader: avatarUrl 或 targetNode 不能为空");
            onError && onError(new Error("参数不能为空"));
            return;
        }

        // 获取目标节点的Sprite组件
        const sprite = targetNode.getComponent(Sprite);
        if (!sprite) {
            console.error("AvatarLoader: 目标节点没有Sprite组件");
            onError && onError(new Error("目标节点没有Sprite组件"));
            return;
        }

        console.log(`AvatarLoader: 开始加载头像 ${avatarUrl}`);

        // 使用assetManager加载远程图片
        assetManager.loadRemote<ImageAsset>(
            avatarUrl, 
            { ext: '.jpg' }, // 默认扩展名，可以根据需要调整
            (err, imageAsset) => {
                if (err) {
                    console.error("AvatarLoader: 加载头像失败", err);
                    onError && onError(err);
                    return;
                }

                try {
                    // 创建SpriteFrame
                    const spriteFrame = new SpriteFrame();
                    const texture = new Texture2D();
                    texture.image = imageAsset;
                    spriteFrame.texture = texture;

                    // 设置到Sprite组件
                    sprite.spriteFrame = spriteFrame;
                    
                    console.log(`AvatarLoader: 头像加载成功 ${avatarUrl}`);
                    onSuccess && onSuccess();
                } catch (error) {
                    console.error("AvatarLoader: 创建SpriteFrame失败", error);
                    onError && onError(error);
                }
            }
        );
    }

    /**
     * 加载网络头像并设置到指定节点（Promise版本）
     * @param avatarUrl 头像URL
     * @param targetNode 目标节点（应该包含Sprite组件）
     * @returns Promise
     */
    static loadAvatarAsync(avatarUrl: string, targetNode: Node): Promise<void> {
        return new Promise((resolve, reject) => {
            this.loadAvatar(
                avatarUrl, 
                targetNode, 
                () => resolve(), 
                (error) => reject(error)
            );
        });
    }

    /**
     * 批量加载头像
     * @param avatarConfigs 头像配置数组，每个配置包含url和targetNode
     * @param onProgress 进度回调
     * @param onComplete 完成回调
     */
    static loadAvatarsBatch(
        avatarConfigs: Array<{ url: string, targetNode: Node }>,
        onProgress?: (loaded: number, total: number) => void,
        onComplete?: (results: Array<{ success: boolean, error?: any }>) => void
    ): void {
        if (!avatarConfigs || avatarConfigs.length === 0) {
            onComplete && onComplete([]);
            return;
        }

        const results: Array<{ success: boolean, error?: any }> = [];
        let loadedCount = 0;

        avatarConfigs.forEach((config, index) => {
            this.loadAvatar(
                config.url,
                config.targetNode,
                () => {
                    // 成功
                    results[index] = { success: true };
                    loadedCount++;
                    onProgress && onProgress(loadedCount, avatarConfigs.length);
                    
                    if (loadedCount === avatarConfigs.length) {
                        onComplete && onComplete(results);
                    }
                },
                (error) => {
                    // 失败
                    results[index] = { success: false, error };
                    loadedCount++;
                    onProgress && onProgress(loadedCount, avatarConfigs.length);
                    
                    if (loadedCount === avatarConfigs.length) {
                        onComplete && onComplete(results);
                    }
                }
            );
        });
    }

    /**
     * 设置默认头像
     * @param targetNode 目标节点
     * @param defaultSpriteFrame 默认的SpriteFrame
     */
    static setDefaultAvatar(targetNode: Node, defaultSpriteFrame?: SpriteFrame): void {
        const sprite = targetNode.getComponent(Sprite);
        if (!sprite) {
            console.error("AvatarLoader: 目标节点没有Sprite组件");
            return;
        }

        if (defaultSpriteFrame) {
            sprite.spriteFrame = defaultSpriteFrame;
        } else {
            // 如果没有提供默认头像，可以设置为null或保持原样
            console.log("AvatarLoader: 设置默认头像，但未提供默认SpriteFrame");
        }
    }

    /**
     * 清除头像
     * @param targetNode 目标节点
     */
    static clearAvatar(targetNode: Node): void {
        const sprite = targetNode.getComponent(Sprite);
        if (sprite) {
            sprite.spriteFrame = null;
        }
    }
}
