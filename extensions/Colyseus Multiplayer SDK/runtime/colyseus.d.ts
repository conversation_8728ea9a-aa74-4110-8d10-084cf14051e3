// Generated by dts-bundle-generator v9.5.1

import { Packr } from '@colyseus/msgpackr';
import * as httpie from 'httpie';

export interface ITransportEventMap {
	onopen?: ((ev: any) => any) | null;
	onmessage?: ((ev: any) => any) | null;
	onclose?: ((ev: any) => any) | null;
	onerror?: ((ev: any) => any) | null;
}
export interface ITransport {
	isOpen: boolean;
	send(data: Buffer | Uint8Array): void;
	sendUnreliable(data: Buffer | Uint8Array): void;
	connect(url: string, options: any): void;
	close(code?: number, reason?: string): void;
}
declare class Connection implements ITransport {
	transport: ITransport;
	events: ITransportEventMap;
	constructor(protocol?: string);
	connect(url: string, options?: any): void;
	send(data: Buffer | Uint8Array): void;
	sendUnreliable(data: Buffer | Uint8Array): void;
	close(code?: number, reason?: string): void;
	get isOpen(): boolean;
}
declare enum OPERATION {
	ADD = 128,// (10000000) add new structure/primitive
	REPLACE = 0,// (00000001) replace structure/primitive
	DELETE = 64,// (01000000) delete field
	DELETE_AND_MOVE = 96,// () add new structure/primitive
	MOVE_AND_ADD = 160,// () add new structure/primitive
	DELETE_AND_ADD = 192,// (11000000) DELETE field, followed by an ADD
	/**
	 * Collection operations
	 */
	CLEAR = 10,
	/**
	 * ArraySchema operations
	 */
	PUSH = 11,
	UNSHIFT = 12,
	REVERSE = 15,
	MOVE = 32,
	DELETE_BY_REFID = 33,// This operation is only used at ENCODING time. During DECODING, DELETE_BY_REFID is converted to DELETE
	ADD_BY_REFID = 129
}
/**
 * Data types
 */
export type PrimitiveType = "string" | "number" | "boolean" | "int8" | "uint8" | "int16" | "uint16" | "int32" | "uint32" | "int64" | "uint64" | "float32" | "float64" | typeof Schema | object;
export type DefinitionType = PrimitiveType | PrimitiveType[] | {
	array: PrimitiveType;
} | {
	map: PrimitiveType;
} | {
	collection: PrimitiveType;
} | {
	set: PrimitiveType;
};
declare const $track: unique symbol;
declare const $encoder: unique symbol;
declare const $decoder: unique symbol;
declare const $filter: unique symbol;
declare const $getByIndex: unique symbol;
declare const $deleteByIndex: unique symbol;
declare const $changes: unique symbol;
declare const $onEncodeEnd: unique symbol;
declare const $onDecodeEnd: unique symbol;
declare const $descriptors: unique symbol;
declare const $numFields = "$__numFields";
declare const $refTypeFieldIndexes = "$__refTypeFieldIndexes";
declare const $viewFieldIndexes = "$__viewFieldIndexes";
declare const $fieldIndexesByViewTag = "$__fieldIndexesByViewTag";
declare class TypeContext {
	types: {
		[id: number]: typeof Schema;
	};
	schemas: Map<typeof Schema, number>;
	hasFilters: boolean;
	parentFiltered: {
		[typeIdAndParentIndex: string]: boolean;
	};
	/**
	 * For inheritance support
	 * Keeps track of which classes extends which. (parent -> children)
	 */
	static inheritedTypes: Map<typeof Schema, Set<typeof Schema>>;
	static register(target: typeof Schema): void;
	constructor(rootClass?: typeof Schema);
	has(schema: typeof Schema): boolean;
	get(typeid: number): typeof Schema;
	add(schema: typeof Schema, typeid?: number): boolean;
	getTypeId(klass: typeof Schema): number;
	private discoverTypes;
}
export type BufferLike = number[] | ArrayBufferLike;
interface Iterator$1 {
	offset: number;
}
declare function utf8Read(bytes: BufferLike, it: Iterator$1, length: number): string;
declare function int8(bytes: BufferLike, it: Iterator$1): number;
declare function uint8(bytes: BufferLike, it: Iterator$1): any;
declare function int16(bytes: BufferLike, it: Iterator$1): number;
declare function uint16(bytes: BufferLike, it: Iterator$1): number;
declare function int32(bytes: BufferLike, it: Iterator$1): number;
declare function uint32(bytes: BufferLike, it: Iterator$1): number;
declare function float32(bytes: BufferLike, it: Iterator$1): number;
declare function float64(bytes: BufferLike, it: Iterator$1): number;
declare function int64(bytes: BufferLike, it: Iterator$1): number;
declare function uint64(bytes: BufferLike, it: Iterator$1): number;
declare function readFloat32(bytes: BufferLike, it: Iterator$1): number;
declare function readFloat64(bytes: BufferLike, it: Iterator$1): number;
declare function boolean(bytes: BufferLike, it: Iterator$1): boolean;
declare function string(bytes: BufferLike, it: Iterator$1): string;
declare function stringCheck(bytes: BufferLike, it: Iterator$1): boolean;
declare function number(bytes: BufferLike, it: Iterator$1): any;
declare function numberCheck(bytes: BufferLike, it: Iterator$1): boolean;
declare function arrayCheck(bytes: BufferLike, it: Iterator$1): boolean;
declare function switchStructureCheck(bytes: BufferLike, it: Iterator$1): boolean;
declare class Root {
	types: TypeContext;
	protected nextUniqueId: number;
	refCount: {
		[id: number]: number;
	};
	changeTrees: {
		[refId: number]: ChangeTree;
	};
	allChanges: ChangeTree[];
	allFilteredChanges: ChangeTree[];
	changes: ChangeTree[];
	filteredChanges: ChangeTree[];
	constructor(types: TypeContext);
	getNextUniqueId(): number;
	add(changeTree: ChangeTree): boolean;
	remove(changeTree: ChangeTree): number;
	removeChangeFromChangeSet(changeSetName: "allChanges" | "changes" | "filteredChanges" | "allFilteredChanges", changeTree: ChangeTree): void;
	clear(): void;
}
declare class Encoder<T extends Schema = any> {
	static BUFFER_SIZE: number;
	sharedBuffer: Buffer;
	context: TypeContext;
	state: T;
	root: Root;
	constructor(state: T);
	protected setState(state: T): void;
	encode(it?: Iterator$1, view?: StateView, buffer?: Buffer, changeSetName?: "changes" | "allChanges" | "filteredChanges" | "allFilteredChanges", isEncodeAll?: boolean, initialOffset?: number): Buffer;
	encodeAll(it?: Iterator$1, buffer?: Buffer): Buffer;
	encodeAllView(view: StateView, sharedOffset: number, it: Iterator$1, bytes?: Buffer): Buffer;
	debugChanges(field: "changes" | "allFilteredChanges" | "allChanges" | "filteredChanges"): void;
	encodeView(view: StateView, sharedOffset: number, it: Iterator$1, bytes?: Buffer): Buffer;
	onEndEncode(changeTrees?: ChangeTree<any>[]): void;
	discardChanges(): void;
	tryEncodeTypeId(bytes: Buffer, baseType: typeof Schema, targetType: typeof Schema, it: Iterator$1): void;
}
export type MetadataField = {
	type: DefinitionType;
	name: string;
	index: number;
	tag?: number;
	unreliable?: boolean;
	deprecated?: boolean;
};
export type Metadata = {
	[$numFields]: number;
} & // number of fields
{
	[$viewFieldIndexes]: number[];
} & // all field indexes with "view" tag
{
	[$fieldIndexesByViewTag]: {
		[tag: number]: number[];
	};
} & // field indexes by "view" tag
{
	[$refTypeFieldIndexes]: number[];
} & // all field indexes containing Ref types (Schema, ArraySchema, MapSchema, etc)
{
	[field: number]: MetadataField;
} & // index => field name
{
	[field: string]: number;
} & // field name => field metadata
{
	[$descriptors]: {
		[field: string]: PropertyDescriptor;
	};
};
declare const Metadata: {
	addField(metadata: any, index: number, name: string, type: DefinitionType, descriptor?: PropertyDescriptor): void;
	setTag(metadata: Metadata, fieldName: string, tag: number): void;
	setFields(target: any, fields: {
		[field: string]: DefinitionType;
	}): any;
	isDeprecated(metadata: any, field: string): boolean;
	init(klass: any): void;
	initialize(constructor: any, parentMetadata?: Metadata): Metadata;
	isValidInstance(klass: any): boolean;
	getFields(klass: any): {};
};
export type EncodeOperation<T extends Ref = any> = (encoder: Encoder, bytes: Buffer, changeTree: ChangeTree<T>, index: number, operation: OPERATION, it: Iterator$1, isEncodeAll: boolean, hasView: boolean, metadata?: Metadata) => void;
/**
 * Used for decoding only.
 */
export type SchemaCallbacks = {
	[field: string | number]: Function[];
};
declare class ReferenceTracker {
	refs: Map<number, Ref>;
	refIds: WeakMap<Ref, number>;
	refCounts: {
		[refId: number]: number;
	};
	deletedRefs: Set<number>;
	callbacks: {
		[refId: number]: SchemaCallbacks;
	};
	protected nextUniqueId: number;
	getNextUniqueId(): number;
	addRef(refId: number, ref: Ref, incrementCount?: boolean): void;
	removeRef(refId: number): void;
	clearRefs(): void;
	garbageCollectDeletedRefs(): void;
	addCallback(refId: number, fieldOrOperation: string | number, callback: Function): () => void;
	removeCallback(refId: number, field: string | number, callback: Function): void;
}
declare class Decoder<T extends Schema = any> {
	context: TypeContext;
	state: T;
	root: ReferenceTracker;
	currentRefId: number;
	triggerChanges?: (allChanges: DataChange[]) => void;
	constructor(root: T, context?: TypeContext);
	protected setState(root: T): void;
	decode(bytes: Buffer, it?: Iterator$1, ref?: Ref): DataChange<any, string>[];
	getInstanceType(bytes: Buffer, it: Iterator$1, defaultType: typeof Schema): typeof Schema;
	createInstanceOfType(type: typeof Schema): Schema;
	removeChildRefs(ref: Collection, allChanges: DataChange[]): void;
}
export interface DataChange<T = any, F = string> {
	ref: Ref;
	refId: number;
	op: OPERATION;
	field: F;
	dynamicIndex?: number | string;
	value: T;
	previousValue: T;
}
export type DecodeOperation<T extends Schema = any> = (decoder: Decoder<T>, bytes: Buffer, it: decode.Iterator, ref: Ref, allChanges: DataChange[]) => number | void;
declare class MapSchema<V = any, K extends string = string> implements Map<K, V>, Collection<K, V, [
	K,
	V
]> {
	protected childType: new () => V;
	protected $items: Map<K, V>;
	protected $indexes: Map<number, K>;
	protected [$changes]: ChangeTree;
	static [$encoder]: EncodeOperation<any>;
	static [$decoder]: DecodeOperation<any>;
	/**
	 * Determine if a property must be filtered.
	 * - If returns false, the property is NOT going to be encoded.
	 * - If returns true, the property is going to be encoded.
	 *
	 * Encoding with "filters" happens in two steps:
	 * - First, the encoder iterates over all "not owned" properties and encodes them.
	 * - Then, the encoder iterates over all "owned" properties per instance and encodes them.
	 */
	static [$filter](ref: MapSchema, index: number, view: StateView): boolean;
	static is(type: any): boolean;
	constructor(initialValues?: Map<K, V> | Record<K, V>);
	/** Iterator */
	[Symbol.iterator](): IterableIterator<[
		K,
		V
	]>;
	get [Symbol.toStringTag](): string;
	static get [Symbol.species](): typeof MapSchema;
	set(key: K, value: V): this;
	get(key: K): V | undefined;
	delete(key: K): boolean;
	clear(): void;
	has(key: K): boolean;
	forEach(callbackfn: (value: V, key: K, map: Map<K, V>) => void): void;
	entries(): IterableIterator<[
		K,
		V
	]>;
	keys(): IterableIterator<K>;
	values(): IterableIterator<V>;
	get size(): number;
	protected setIndex(index: number, key: K): void;
	protected getIndex(index: number): K;
	protected [$getByIndex](index: number): V;
	protected [$deleteByIndex](index: number): void;
	protected [$onEncodeEnd](): void;
	toJSON(): any;
	clone(isDecoding?: boolean): MapSchema<V>;
}
export type K = number;
declare class CollectionSchema<V = any> implements Collection<K, V> {
	protected $items: Map<number, V>;
	protected $indexes: Map<number, number>;
	protected $refId: number;
	static [$encoder]: EncodeOperation<any>;
	static [$decoder]: DecodeOperation<any>;
	/**
	 * Determine if a property must be filtered.
	 * - If returns false, the property is NOT going to be encoded.
	 * - If returns true, the property is going to be encoded.
	 *
	 * Encoding with "filters" happens in two steps:
	 * - First, the encoder iterates over all "not owned" properties and encodes them.
	 * - Then, the encoder iterates over all "owned" properties per instance and encodes them.
	 */
	static [$filter](ref: CollectionSchema, index: number, view: StateView): boolean;
	static is(type: any): boolean;
	constructor(initialValues?: Array<V>);
	add(value: V): number;
	at(index: number): V | undefined;
	entries(): IterableIterator<[
		number,
		V
	]>;
	delete(item: V): boolean;
	clear(): void;
	has(value: V): boolean;
	forEach(callbackfn: (value: V, key: K, collection: CollectionSchema<V>) => void): void;
	values(): IterableIterator<V>;
	get size(): number;
	/** Iterator */
	[Symbol.iterator](): IterableIterator<V>;
	protected setIndex(index: number, key: number): void;
	protected getIndex(index: number): number;
	protected [$getByIndex](index: number): V;
	protected [$deleteByIndex](index: number): void;
	toArray(): V[];
	toJSON(): V[];
	clone(isDecoding?: boolean): CollectionSchema<V>;
}
declare class SetSchema<V = any> implements Collection<number, V> {
	protected $items: Map<number, V>;
	protected $indexes: Map<number, number>;
	protected $refId: number;
	static [$encoder]: EncodeOperation<any>;
	static [$decoder]: DecodeOperation<any>;
	/**
	 * Determine if a property must be filtered.
	 * - If returns false, the property is NOT going to be encoded.
	 * - If returns true, the property is going to be encoded.
	 *
	 * Encoding with "filters" happens in two steps:
	 * - First, the encoder iterates over all "not owned" properties and encodes them.
	 * - Then, the encoder iterates over all "owned" properties per instance and encodes them.
	 */
	static [$filter](ref: SetSchema, index: number, view: StateView): boolean;
	static is(type: any): boolean;
	constructor(initialValues?: Array<V>);
	add(value: V): number | false;
	entries(): IterableIterator<[
		number,
		V
	]>;
	delete(item: V): boolean;
	clear(): void;
	has(value: V): boolean;
	forEach(callbackfn: (value: V, key: number, collection: SetSchema<V>) => void): void;
	values(): IterableIterator<V>;
	get size(): number;
	/** Iterator */
	[Symbol.iterator](): IterableIterator<V>;
	protected setIndex(index: number, key: number): void;
	protected getIndex(index: number): number;
	protected [$getByIndex](index: number): V;
	protected [$deleteByIndex](index: number): void;
	toArray(): V[];
	toJSON(): V[];
	clone(isDecoding?: boolean): SetSchema<V>;
}
export type Ref = Schema | ArraySchema | MapSchema | CollectionSchema | SetSchema;
export interface IndexedOperations {
	[index: number]: OPERATION;
}
export interface ChangeSet {
	indexes: {
		[index: number]: number;
	};
	operations: OPERATION[];
	queueRootIndex?: number;
}
declare class ChangeTree<T extends Ref = any> {
	ref: T;
	refId: number;
	root?: Root;
	parent?: Ref;
	parentIndex?: number;
	isFiltered: boolean;
	isPartiallyFiltered: boolean;
	indexedOperations: IndexedOperations;
	changes: ChangeSet;
	allChanges: ChangeSet;
	filteredChanges: ChangeSet;
	allFilteredChanges: ChangeSet;
	indexes: {
		[index: string]: any;
	};
	/**
	 * Is this a new instance? Used on ArraySchema to determine OPERATION.MOVE_AND_ADD operation.
	 */
	isNew: boolean;
	constructor(ref: T);
	setRoot(root: Root): void;
	setParent(parent: Ref, root?: Root, parentIndex?: number): void;
	forEachChild(callback: (change: ChangeTree, atIndex: number) => void): void;
	operation(op: OPERATION): void;
	change(index: number, operation?: OPERATION): void;
	shiftChangeIndexes(shiftIndex: number): void;
	shiftAllChangeIndexes(shiftIndex: number, startIndex?: number): void;
	private _shiftAllChangeIndexes;
	indexedOperation(index: number, operation: OPERATION, allChangesIndex?: number): void;
	getType(index?: number): any;
	getChange(index: number): OPERATION;
	getValue(index: number, isEncodeAll?: boolean): any;
	delete(index: number, operation?: OPERATION, allChangesIndex?: number): void;
	endEncode(): void;
	discard(discardAll?: boolean): void;
	/**
	 * Recursively discard all changes from this, and child structures.
	 */
	discardAll(): void;
	ensureRefId(): void;
	get changed(): boolean;
	protected checkIsFiltered(metadata: Metadata, parent: Ref, parentIndex: number): void;
}
declare class StateView {
	/**
	 * List of ChangeTree's that are visible to this view
	 */
	items: WeakSet<ChangeTree>;
	/**
	 * List of ChangeTree's that are invisible to this view
	 */
	invisible: WeakSet<ChangeTree>;
	tags?: WeakMap<ChangeTree, Set<number>>;
	/**
	 * Manual "ADD" operations for changes per ChangeTree, specific to this view.
	 * (This is used to force encoding a property, even if it was not changed)
	 */
	changes: {
		[refId: number]: IndexedOperations;
	};
	add(obj: Ref, tag?: number, checkIncludeParent?: boolean): this;
	protected addParent(changeTree: ChangeTree, parentIndex: number, tag: number): void;
	remove(obj: Ref, tag?: number): this;
}
declare class ArraySchema<V = any> implements Array<V>, Collection<number, V> {
	[n: number]: V;
	protected items: V[];
	protected tmpItems: V[];
	protected deletedIndexes: {
		[index: number]: boolean;
	};
	static [$encoder]: EncodeOperation<any>;
	static [$decoder]: DecodeOperation<any>;
	/**
	 * Determine if a property must be filtered.
	 * - If returns false, the property is NOT going to be encoded.
	 * - If returns true, the property is going to be encoded.
	 *
	 * Encoding with "filters" happens in two steps:
	 * - First, the encoder iterates over all "not owned" properties and encodes them.
	 * - Then, the encoder iterates over all "owned" properties per instance and encodes them.
	 */
	static [$filter](ref: ArraySchema, index: number, view: StateView): boolean;
	static is(type: any): boolean;
	constructor(...items: V[]);
	set length(newLength: number);
	get length(): number;
	push(...values: V[]): number;
	/**
	 * Removes the last element from an array and returns it.
	 */
	pop(): V | undefined;
	at(index: number): V;
	protected $changeAt(index: number, value: V): void;
	protected $deleteAt(index: number, operation?: OPERATION): void;
	protected $setAt(index: number, value: V, operation: OPERATION): void;
	clear(): void;
	/**
	 * Combines two or more arrays.
	 * @param items Additional items to add to the end of array1.
	 */
	concat(...items: (V | ConcatArray<V>)[]): ArraySchema<V>;
	/**
	 * Adds all the elements of an array separated by the specified separator string.
	 * @param separator A string used to separate one element of an array from the next in the resulting String. If omitted, the array elements are separated with a comma.
	 */
	join(separator?: string): string;
	/**
	 * Reverses the elements in an Array.
	 */
	reverse(): ArraySchema<V>;
	/**
	 * Removes the first element from an array and returns it.
	 */
	shift(): V | undefined;
	/**
	 * Returns a section of an array.
	 * @param start The beginning of the specified portion of the array.
	 * @param end The end of the specified portion of the array. This is exclusive of the element at the index 'end'.
	 */
	slice(start?: number, end?: number): V[];
	/**
	 * Sorts an array.
	 * @param compareFn Function used to determine the order of the elements. It is expected to return
	 * a negative value if first argument is less than second argument, zero if they're equal and a positive
	 * value otherwise. If omitted, the elements are sorted in ascending, ASCII character order.
	 * ```ts
	 * [11,2,22,1].sort((a, b) => a - b)
	 * ```
	 */
	sort(compareFn?: (a: V, b: V) => number): this;
	/**
	 * Removes elements from an array and, if necessary, inserts new elements in their place, returning the deleted elements.
	 * @param start The zero-based location in the array from which to start removing elements.
	 * @param deleteCount The number of elements to remove.
	 * @param insertItems Elements to insert into the array in place of the deleted elements.
	 */
	splice(start: number, deleteCount?: number, ...insertItems: V[]): V[];
	/**
	 * Inserts new elements at the start of an array.
	 * @param items  Elements to insert at the start of the Array.
	 */
	unshift(...items: V[]): number;
	/**
	 * Returns the index of the first occurrence of a value in an array.
	 * @param searchElement The value to locate in the array.
	 * @param fromIndex The array index at which to begin the search. If fromIndex is omitted, the search starts at index 0.
	 */
	indexOf(searchElement: V, fromIndex?: number): number;
	/**
	 * Returns the index of the last occurrence of a specified value in an array.
	 * @param searchElement The value to locate in the array.
	 * @param fromIndex The array index at which to begin the search. If fromIndex is omitted, the search starts at the last index in the array.
	 */
	lastIndexOf(searchElement: V, fromIndex?: number): number;
	/**
	 * Determines whether all the members of an array satisfy the specified test.
	 * @param callbackfn A function that accepts up to three arguments. The every method calls
	 * the callbackfn function for each element in the array until the callbackfn returns a value
	 * which is coercible to the Boolean value false, or until the end of the array.
	 * @param thisArg An object to which the this keyword can refer in the callbackfn function.
	 * If thisArg is omitted, undefined is used as the this value.
	 */
	every<S extends V>(predicate: (value: V, index: number, array: V[]) => value is S, thisArg?: any): this is S[];
	/**
	 * Determines whether the specified callback function returns true for any element of an array.
	 * @param callbackfn A function that accepts up to three arguments. The some method calls
	 * the callbackfn function for each element in the array until the callbackfn returns a value
	 * which is coercible to the Boolean value true, or until the end of the array.
	 * @param thisArg An object to which the this keyword can refer in the callbackfn function.
	 * If thisArg is omitted, undefined is used as the this value.
	 */
	some(callbackfn: (value: V, index: number, array: V[]) => unknown, thisArg?: any): boolean;
	/**
	 * Performs the specified action for each element in an array.
	 * @param callbackfn  A function that accepts up to three arguments. forEach calls the callbackfn function one time for each element in the array.
	 * @param thisArg  An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.
	 */
	forEach(callbackfn: (value: V, index: number, array: V[]) => void, thisArg?: any): void;
	/**
	 * Calls a defined callback function on each element of an array, and returns an array that contains the results.
	 * @param callbackfn A function that accepts up to three arguments. The map method calls the callbackfn function one time for each element in the array.
	 * @param thisArg An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.
	 */
	map<U>(callbackfn: (value: V, index: number, array: V[]) => U, thisArg?: any): U[];
	/**
	 * Returns the elements of an array that meet the condition specified in a callback function.
	 * @param callbackfn A function that accepts up to three arguments. The filter method calls the callbackfn function one time for each element in the array.
	 * @param thisArg An object to which the this keyword can refer in the callbackfn function. If thisArg is omitted, undefined is used as the this value.
	 */
	filter(callbackfn: (value: V, index: number, array: V[]) => unknown, thisArg?: any): V[];
	/**
	 * Calls the specified callback function for all the elements in an array. The return value of the callback function is the accumulated result, and is provided as an argument in the next call to the callback function.
	 * @param callbackfn A function that accepts up to four arguments. The reduce method calls the callbackfn function one time for each element in the array.
	 * @param initialValue If initialValue is specified, it is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument instead of an array value.
	 */
	reduce<U = V>(callbackfn: (previousValue: U, currentValue: V, currentIndex: number, array: V[]) => U, initialValue?: U): U;
	/**
	 * Calls the specified callback function for all the elements in an array, in descending order. The return value of the callback function is the accumulated result, and is provided as an argument in the next call to the callback function.
	 * @param callbackfn A function that accepts up to four arguments. The reduceRight method calls the callbackfn function one time for each element in the array.
	 * @param initialValue If initialValue is specified, it is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument instead of an array value.
	 */
	reduceRight<U = V>(callbackfn: (previousValue: U, currentValue: V, currentIndex: number, array: V[]) => U, initialValue?: U): U;
	/**
	 * Returns the value of the first element in the array where predicate is true, and undefined
	 * otherwise.
	 * @param predicate find calls predicate once for each element of the array, in ascending
	 * order, until it finds one where predicate returns true. If such an element is found, find
	 * immediately returns that element value. Otherwise, find returns undefined.
	 * @param thisArg If provided, it will be used as the this value for each invocation of
	 * predicate. If it is not provided, undefined is used instead.
	 */
	find(predicate: (value: V, index: number, obj: V[]) => boolean, thisArg?: any): V | undefined;
	/**
	 * Returns the index of the first element in the array where predicate is true, and -1
	 * otherwise.
	 * @param predicate find calls predicate once for each element of the array, in ascending
	 * order, until it finds one where predicate returns true. If such an element is found,
	 * findIndex immediately returns that element index. Otherwise, findIndex returns -1.
	 * @param thisArg If provided, it will be used as the this value for each invocation of
	 * predicate. If it is not provided, undefined is used instead.
	 */
	findIndex(predicate: (value: V, index: number, obj: V[]) => unknown, thisArg?: any): number;
	/**
	 * Returns the this object after filling the section identified by start and end with value
	 * @param value value to fill array section with
	 * @param start index to start filling the array at. If start is negative, it is treated as
	 * length+start where length is the length of the array.
	 * @param end index to stop filling the array at. If end is negative, it is treated as
	 * length+end.
	 */
	fill(value: V, start?: number, end?: number): this;
	/**
	 * Returns the this object after copying a section of the array identified by start and end
	 * to the same array starting at position target
	 * @param target If target is negative, it is treated as length+target where length is the
	 * length of the array.
	 * @param start If start is negative, it is treated as length+start. If end is negative, it
	 * is treated as length+end.
	 * @param end If not specified, length of the this object is used as its default value.
	 */
	copyWithin(target: number, start: number, end?: number): this;
	/**
	 * Returns a string representation of an array.
	 */
	toString(): string;
	/**
	 * Returns a string representation of an array. The elements are converted to string using their toLocalString methods.
	 */
	toLocaleString(): string;
	/** Iterator */
	[Symbol.iterator](): IterableIterator<V>;
	static get [Symbol.species](): typeof ArraySchema;
	[Symbol.unscopables]: any;
	/**
	 * Returns an iterable of key, value pairs for every entry in the array
	 */
	entries(): IterableIterator<[
		number,
		V
	]>;
	/**
	 * Returns an iterable of keys in the array
	 */
	keys(): IterableIterator<number>;
	/**
	 * Returns an iterable of values in the array
	 */
	values(): IterableIterator<V>;
	/**
	 * Determines whether an array includes a certain element, returning true or false as appropriate.
	 * @param searchElement The element to search for.
	 * @param fromIndex The position in this array at which to begin searching for searchElement.
	 */
	includes(searchElement: V, fromIndex?: number): boolean;
	/**
	 * Calls a defined callback function on each element of an array. Then, flattens the result into
	 * a new array.
	 * This is identical to a map followed by flat with depth 1.
	 *
	 * @param callback A function that accepts up to three arguments. The flatMap method calls the
	 * callback function one time for each element in the array.
	 * @param thisArg An object to which the this keyword can refer in the callback function. If
	 * thisArg is omitted, undefined is used as the this value.
	 */
	flatMap<U, This = undefined>(callback: (this: This, value: V, index: number, array: V[]) => U | ReadonlyArray<U>, thisArg?: This): U[];
	/**
	 * Returns a new array with all sub-array elements concatenated into it recursively up to the
	 * specified depth.
	 *
	 * @param depth The maximum recursion depth
	 */
	flat<A, D extends number = 1>(this: A, depth?: D): any;
	findLast(): any;
	findLastIndex(...args: any[]): any;
	with(index: number, value: V): ArraySchema<V>;
	toReversed(): V[];
	toSorted(compareFn?: (a: V, b: V) => number): V[];
	toSpliced(start: number, deleteCount: number, ...items: V[]): V[];
	toSpliced(start: number, deleteCount?: number): V[];
	protected [$getByIndex](index: number, isEncodeAll?: boolean): V;
	protected [$deleteByIndex](index: number): void;
	protected [$onEncodeEnd](): void;
	protected [$onDecodeEnd](): void;
	toArray(): V[];
	toJSON(): any[];
	clone(isDecoding?: boolean): ArraySchema<V>;
}
export interface Collection<K = any, V = any, IT = V> {
	[Symbol.iterator](): IterableIterator<IT>;
	forEach(callback: Function): any;
	entries(): IterableIterator<[
		K,
		V
	]>;
}
export type NonFunctionProps<T> = Omit<T, {
	[K in keyof T]: T[K] extends Function ? K : never;
}[keyof T]>;
export type NonFunctionPropNames<T> = {
	[K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];
export type NonFunctionNonPrimitivePropNames<T> = {
	[K in keyof T]: T[K] extends Function ? never : T[K] extends number | string | boolean ? never : K;
}[keyof T];
export type ToJSON<T> = NonFunctionProps<{
	[K in keyof T]: T[K] extends MapSchema<infer U> ? Record<string, U> : T[K] extends Map<string, infer U> ? Record<string, U> : T[K] extends ArraySchema<infer U> ? U[] : T[K];
}>;
declare abstract class Schema {
	static [$encoder]: EncodeOperation<any>;
	static [$decoder]: DecodeOperation<any>;
	/**
	 * Assign the property descriptors required to track changes on this instance.
	 * @param instance
	 */
	static initialize(instance: any): void;
	static is(type: DefinitionType): boolean;
	/**
	 * Track property changes
	 */
	static [$track](changeTree: ChangeTree, index: number, operation?: OPERATION): void;
	/**
	 * Determine if a property must be filtered.
	 * - If returns false, the property is NOT going to be encoded.
	 * - If returns true, the property is going to be encoded.
	 *
	 * Encoding with "filters" happens in two steps:
	 * - First, the encoder iterates over all "not owned" properties and encodes them.
	 * - Then, the encoder iterates over all "owned" properties per instance and encodes them.
	 */
	static [$filter](ref: Schema, index: number, view: StateView): boolean;
	constructor(...args: any[]);
	assign(props: {
		[prop in NonFunctionPropNames<this>]?: this[prop];
	} | ToJSON<this>): this;
	/**
	 * (Server-side): Flag a property to be encoded for the next patch.
	 * @param instance Schema instance
	 * @param property string representing the property name, or number representing the index of the property.
	 * @param operation OPERATION to perform (detected automatically)
	 */
	setDirty<K extends NonFunctionPropNames<this>>(property: K | number, operation?: OPERATION): void;
	clone(): this;
	toJSON(): ToJSON<typeof Schema>;
	discardAllChanges(): void;
	protected [$getByIndex](index: number): any;
	protected [$deleteByIndex](index: number): void;
	static debugRefIds(instance: Ref, jsonContents?: boolean, level?: number): string;
	/**
	 * Return a string representation of the changes on a Schema instance.
	 * The list of changes is cleared after each encode.
	 *
	 * @param instance Schema instance
	 * @param isEncodeAll Return "full encode" instead of current change set.
	 * @returns
	 */
	static debugChanges(instance: Ref, isEncodeAll?: boolean): string;
	static debugChangesDeep(ref: Ref, changeSetName?: "changes" | "allChanges" | "allFilteredChanges" | "filteredChanges"): string;
}
export type GetCallbackProxy = (<T extends Schema>(instance: T) => CallbackProxy<T>);
export type CallbackProxy<T> = unknown extends T ? InstanceCallback<T> & CollectionCallback<any, any> : T extends Collection<infer K, infer V, infer _> ? CollectionCallback<K, V> : InstanceCallback<T>;
export type InstanceCallback<T> = {
	/**
	 * Trigger callback when value of a property changes.
	 *
	 * @param prop name of the property
	 * @param callback callback to be triggered on property change
	 * @param immediate trigger immediatelly if property has been already set.
	 * @return callback to detach the listener
	 */
	listen<K extends NonFunctionPropNames<T>>(prop: K, callback: (value: T[K], previousValue: T[K]) => void, immediate?: boolean): () => void;
	/**
	 * Trigger callback whenever any property changed within this instance.
	 *
	 * @param prop name of the property
	 * @param callback callback to be triggered on property change
	 * @param immediate trigger immediatelly if property has been already set.
	 * @return callback to detach the listener
	 */
	onChange(callback: () => void): () => void;
	/**
	 * Bind properties to another object. Changes on the properties will be reflected on the target object.
	 *
	 * @param targetObject object to bind properties to
	 * @param properties list of properties to bind. If not provided, all properties will be bound.
	 */
	bindTo(targetObject: any, properties?: Array<NonFunctionPropNames<T>>): void;
} & {
	[K in NonFunctionNonPrimitivePropNames<T>]: CallbackProxy<T[K]>;
};
export type CollectionCallback<K, V> = {
	/**
	 * Trigger callback when an item has been added to the collection.
	 *
	 * @param callback
	 * @param immediate
	 * @return callback to detach the onAdd listener
	 */
	onAdd(callback: (item: V, index: K) => void, immediate?: boolean): () => void;
	/**
	 * Trigger callback when an item has been removed to the collection.
	 *
	 * @param callback
	 * @return callback to detach the onRemove listener
	 */
	onRemove(callback: (item: V, index: K) => void): () => void;
};
type BufferLike$1 = number[] | Uint8Array | Buffer;
export interface Serializer<State> {
	setState(data: BufferLike$1, it?: Iterator$1): void;
	getState(): State;
	patch(data: BufferLike$1, it?: Iterator$1): void;
	teardown(): void;
	handshake?(bytes: BufferLike$1, it?: any): void;
}
export declare function registerSerializer(id: string, serializer: any): void;
export type SchemaConstructor<T = Schema> = new (...args: any[]) => T;
export declare function getStateCallbacks<T extends Schema>(room: Room<T>): GetCallbackProxy;
export declare class SchemaSerializer<T extends Schema = any> implements Serializer<T> {
	state: T;
	decoder: Decoder<T>;
	setState(encodedState: Buffer, it?: Iterator$1): void;
	getState(): T;
	patch(patches: Buffer, it?: Iterator$1): DataChange<any, string>[];
	teardown(): void;
	handshake(bytes: Buffer, it?: Iterator$1): void;
}
export type FunctionParameters<T extends (...args: any[]) => any> = T extends (...args: infer P) => any ? P : never;
declare class EventEmitter<CallbackSignature extends (...args: any[]) => any> {
	handlers: Array<CallbackSignature>;
	register(cb: CallbackSignature, once?: boolean): this;
	invoke(...args: FunctionParameters<CallbackSignature>): void;
	invokeAsync(...args: FunctionParameters<CallbackSignature>): Promise<any[]>;
	remove(cb: CallbackSignature): void;
	clear(): void;
}
export interface RoomAvailable<Metadata = any> {
	name: string;
	roomId: string;
	clients: number;
	maxClients: number;
	metadata?: Metadata;
}
export declare class Room<State = any> {
	roomId: string;
	sessionId: string;
	reconnectionToken: string;
	name: string;
	connection: Connection;
	onStateChange: {
		once: (cb: (state: State) => void) => void;
		remove: (cb: (state: State) => void) => void;
		invoke: (state: State) => void;
		invokeAsync: (state: State) => Promise<any[]>;
		clear: () => void;
	} & ((this: any, cb: (state: State) => void) => EventEmitter<(state: State) => void>);
	onError: {
		once: (cb: (code: number, message?: string) => void) => void;
		remove: (cb: (code: number, message?: string) => void) => void;
		invoke: (code: number, message?: string) => void;
		invokeAsync: (code: number, message?: string) => Promise<any[]>;
		clear: () => void;
	} & ((this: any, cb: (code: number, message?: string) => void) => EventEmitter<(code: number, message?: string) => void>);
	onLeave: {
		once: (cb: (code: number) => void) => void;
		remove: (cb: (code: number) => void) => void;
		invoke: (code: number) => void;
		invokeAsync: (code: number) => Promise<any[]>;
		clear: () => void;
	} & ((this: any, cb: (code: number) => void) => EventEmitter<(code: number) => void>);
	protected onJoin: {
		once: (cb: (...args: any[]) => void | Promise<any>) => void;
		remove: (cb: (...args: any[]) => void | Promise<any>) => void;
		invoke: (...args: any[]) => void;
		invokeAsync: (...args: any[]) => Promise<any[]>;
		clear: () => void;
	} & ((this: any, cb: (...args: any[]) => void | Promise<any>) => EventEmitter<(...args: any[]) => void | Promise<any>>);
	serializerId: string;
	serializer: Serializer<State>;
	protected hasJoined: boolean;
	protected rootSchema: SchemaConstructor<State>;
	protected onMessageHandlers: {
		emit(event: string, ...args: any[]): void;
		events: {};
		on(event: string, cb: (...args: any[]) => void): () => void;
	};
	protected packr: Packr;
	constructor(name: string, rootSchema?: SchemaConstructor<State>);
	connect(endpoint: string, devModeCloseCallback?: () => void, room?: Room, // when reconnecting on devMode, re-use previous room intance for handling events.
	options?: any): void;
	leave(consented?: boolean): Promise<number>;
	onMessage<T = any>(type: "*", callback: (type: string | number, message: T) => void): any;
	onMessage<T = any>(type: string | number, callback: (message: T) => void): any;
	send<T = any>(type: string | number, message?: T): void;
	sendUnreliable<T = any>(type: string | number, message?: T): void;
	sendBytes(type: string | number, bytes: Uint8Array): void;
	get state(): State;
	removeAllListeners(): void;
	protected onMessageCallback(event: MessageEvent): void;
	private dispatchMessage;
	private destroy;
	private getMessageHandlerKey;
}
declare class HTTP {
	protected client: Client;
	authToken: string;
	constructor(client: Client);
	get<T = any>(path: string, options?: Partial<httpie.Options>): Promise<httpie.Response<T>>;
	post<T = any>(path: string, options?: Partial<httpie.Options>): Promise<httpie.Response<T>>;
	del<T = any>(path: string, options?: Partial<httpie.Options>): Promise<httpie.Response<T>>;
	put<T = any>(path: string, options?: Partial<httpie.Options>): Promise<httpie.Response<T>>;
	protected request(method: "get" | "post" | "put" | "del", path: string, options?: Partial<httpie.Options>): Promise<httpie.Response>;
	protected getOptions(options: Partial<httpie.Options>): Partial<httpie.Options>;
}
export interface AuthSettings {
	path: string;
	key: string;
}
export interface PopupSettings {
	prefix: string;
	width: number;
	height: number;
}
export interface AuthData {
	user: any;
	token: string;
}
export declare class Auth {
	#private;
	protected http: HTTP;
	settings: AuthSettings;
	constructor(http: HTTP);
	set token(token: string);
	get token(): string;
	onChange(callback: (response: AuthData) => void): () => void;
	getUserData(): Promise<any>;
	registerWithEmailAndPassword(email: string, password: string, options?: any): Promise<any>;
	signInWithEmailAndPassword(email: string, password: string): Promise<any>;
	signInAnonymously(options?: any): Promise<any>;
	sendPasswordResetEmail(email: string): Promise<any>;
	signInWithProvider(providerName: string, settings?: Partial<PopupSettings>): Promise<unknown>;
	signOut(): Promise<void>;
	private emitChange;
}
export interface SeatReservation {
	room: RoomAvailable;
	sessionId: string;
	reconnectionToken?: string;
	devMode?: boolean;
	protocol?: string;
}
export declare enum Protocol {
	HANDSHAKE = 9,
	JOIN_ROOM = 10,
	ERROR = 11,
	LEAVE_ROOM = 12,
	ROOM_DATA = 13,
	ROOM_STATE = 14,
	ROOM_STATE_PATCH = 15,
	ROOM_DATA_SCHEMA = 16,
	ROOM_DATA_BYTES = 17
}
export declare enum ErrorCode {
	MATCHMAKE_NO_HANDLER = 4210,
	MATCHMAKE_INVALID_CRITERIA = 4211,
	MATCHMAKE_INVALID_ROOM_ID = 4212,
	MATCHMAKE_UNHANDLED = 4213,
	MATCHMAKE_EXPIRED = 4214,
	AUTH_FAILED = 4215,
	APPLICATION_ERROR = 4216
}
export type JoinOptions = any;
export interface EndpointSettings {
	hostname: string;
	secure: boolean;
	port?: number;
	pathname?: string;
}
export declare class Client {
	static VERSION: string;
	http: HTTP;
	auth: Auth;
	protected settings: EndpointSettings;
	protected urlBuilder: (url: URL) => string;
	constructor(settings?: string | EndpointSettings, customURLBuilder?: (url: URL) => string);
	joinOrCreate<T>(roomName: string, options?: JoinOptions, rootSchema?: SchemaConstructor<T>): Promise<Room<T>>;
	create<T>(roomName: string, options?: JoinOptions, rootSchema?: SchemaConstructor<T>): Promise<Room<T>>;
	join<T>(roomName: string, options?: JoinOptions, rootSchema?: SchemaConstructor<T>): Promise<Room<T>>;
	joinById<T>(roomId: string, options?: JoinOptions, rootSchema?: SchemaConstructor<T>): Promise<Room<T>>;
	/**
	 * Re-establish connection with a room this client was previously connected to.
	 *
	 * @param reconnectionToken The `room.reconnectionToken` from previously connected room.
	 * @param rootSchema (optional) Concrete root schema definition
	 * @returns Promise<Room>
	 */
	reconnect<T>(reconnectionToken: string, rootSchema?: SchemaConstructor<T>): Promise<Room<T>>;
	getAvailableRooms<Metadata = any>(roomName?: string): Promise<RoomAvailable<Metadata>[]>;
	consumeSeatReservation<T>(response: SeatReservation, rootSchema?: SchemaConstructor<T>, reuseRoomInstance?: Room): Promise<Room<T>>;
	protected createMatchMakeRequest<T>(method: string, roomName: string, options?: JoinOptions, rootSchema?: SchemaConstructor<T>, reuseRoomInstance?: Room): Promise<Room<T>>;
	protected createRoom<T>(roomName: string, rootSchema?: SchemaConstructor<T>): Room<T>;
	protected buildEndpoint(room: any, options?: any, protocol?: string): string;
	protected getHttpEndpoint(segments?: string): string;
	protected getEndpointPort(): string;
}

declare namespace decode {
	export { Iterator$1 as Iterator, arrayCheck, boolean, float32, float64, int16, int32, int64, int8, number, numberCheck, readFloat32, readFloat64, string, stringCheck, switchStructureCheck, uint16, uint32, uint64, uint8, utf8Read };
}

export as namespace Colyseus;

export {};
